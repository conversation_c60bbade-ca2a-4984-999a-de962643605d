import { useCallback, useMemo } from "react";
import { useParams } from "react-router";
import { toast } from "sonner";
import { useEncryption, base64ToUint8Array } from "~/hooks/useEncryption";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { usePassword } from "~/store/credentials";
import {
  validateReportForm,
  formatSearchArgs,
  getPlainTextValue,
  isRelacoesReport,
  isValidEmail,
  shouldValidateEmail,
  type CreateReportFormData
} from "~/helpers/createReport.helper";
import {
  useNewReportActions,
  useNewReportInputValue,
  useNewReportSelectedType,
  useDocument1Type,
  useDocument1Value,
  useDocument2Type,
  useDocument2Value,
  useReportMode,
  useIgnoreConsortiums,
  useIgnoreOtherContacts,
} from "~/store/newReportStore";

export function useCreateReportForm() {
  const { folderId } = useParams<{ folderId?: string }>();
  const { newReportMutation } = useReportCRUD(folderId);
  const { _encryptData } = useEncryption();
  const secretKey = usePassword();
  const selectedReportType = useNewReportSelectedType();
  const reportInputValue = useNewReportInputValue();
  const document1Type = useDocument1Type();
  const document1Value = useDocument1Value();
  const document2Type = useDocument2Type();
  const document2Value = useDocument2Value();
  const reportMode = useReportMode();
  const ignoreConsortiums = useIgnoreConsortiums();
  const ignoreOtherContacts = useIgnoreOtherContacts();

  const {
    setSelectedReportType,
    setReportInputValue,
    setDocument1Type,
    setDocument1Value,
    setDocument2Type,
    setDocument2Value,
    setReportMode,
    setIgnoreConsortiums,
    setIgnoreOtherContacts,
    clearNewReportValues
  } = useNewReportActions();

  const formData: CreateReportFormData = useMemo(() => ({
    selectedReportType,
    reportInputValue,
    document1Type,
    document1Value,
    document2Type,
    document2Value,
    reportMode,
    ignoreConsortiums,
    ignoreOtherContacts
  }), [
    selectedReportType,
    reportInputValue,
    document1Type,
    document1Value,
    document2Type,
    document2Value,
    reportMode,
    ignoreConsortiums,
    ignoreOtherContacts
  ]);

  const isFormValid = useMemo(() => validateReportForm(formData), [formData]);

  const handleCreateReport = useCallback(async () => {
    const isRelacoesType = isRelacoesReport(selectedReportType);

    if (isRelacoesType) {
      if (!document1Type || !document1Value || !document2Type || !document2Value) {
        toast("Preencha todos os campos", {
          description: "Necessário selecionar os tipos e preencher os valores dos dois documentos",
          action: { label: "Fechar", onClick: () => { } },
        });
        return;
      }
    } else {
      if (!selectedReportType || !reportInputValue) {
        toast("Preencha todos os campos", {
          description: "Necessário tipo de entrada e seu valor",
          action: { label: "Fechar", onClick: () => { } },
        });
        return;
      }

      if (shouldValidateEmail(selectedReportType, reportInputValue) && !isValidEmail(reportInputValue)) {
        toast("Email inválido", {
          description: "Email inválido",
          action: { label: "Fechar", onClick: () => { } },
        });
        return;
      }
    }

    const searchArgsFormatInput = formatSearchArgs(formData);
    const plainTextValue = getPlainTextValue(formData);

    try {
      const derivedKey = base64ToUint8Array(secretKey || "");
      const encryptedValue = await _encryptData(searchArgsFormatInput, derivedKey.buffer);

      if (!encryptedValue.data) {
        toast("Erro", {
          description: "Erro ao criptografar dados",
          action: { label: "Fechar", onClick: () => { } },
        });
        return;
      }

      await newReportMutation.mutateAsync({
        report_type: selectedReportType,
        report_input_value: plainTextValue,
        report_search_args: searchArgsFormatInput,
        report_input_encrypted: encryptedValue.data,
        parent_folder_id: folderId || null,
      });
    } catch (error) {
      console.error("Error creating new report:", error);
      toast("Erro", {
        description: "Erro ao criar relatório",
        action: { label: "Fechar", onClick: () => { } },
      });
    }
  }, [
    selectedReportType,
    reportInputValue,
    document1Type,
    document1Value,
    document2Type,
    document2Value,
    ignoreConsortiums,
    ignoreOtherContacts,
    folderId,
    secretKey,
    _encryptData,
    newReportMutation,
    formData
  ]);

  return {
    formData,
    isFormValid,
    isLoading: newReportMutation.isPending,
    setSelectedReportType,
    setReportInputValue,
    setDocument1Type,
    setDocument1Value,
    setDocument2Type,
    setDocument2Value,
    setReportMode,
    setIgnoreConsortiums,
    setIgnoreOtherContacts,
    clearNewReportValues,
    handleCreateReport,
  };
}
