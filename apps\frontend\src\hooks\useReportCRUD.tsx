import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  fetchReportList,
  fetchReportById,
  createNewReport,
  addNewReport,
  deleteReport,
  generatePDF,
  renameReport,
  mergeReports,
} from "~/services/gateways/report.gateway";
import { base64ToUint8Array, useEncryption } from "~/hooks/useEncryption";
import type {
  EncryptedPayload,
  ReportData,
  ReportPDFProps,
  NewReportRequest,
  MergeReportsPayload,
  MergeReportsProps
} from "~/types/global";
import { FolderData } from "root/domain/entities/folder.model";
import { toast } from "sonner";
import { useDialogActions } from "~/store/dialogStore";
import { useNewPendingReportsActions } from "~/store/newReportStatusStore";
import {
  decryptReportPayload,
  encryptReportPayload,
  combineReportSearchArgs,
  combineNgramsFromReports,
  fetchAndDecryptReports
} from "~/helpers";
import { REPORT_CONSTANTS, REPORT_TYPES, tanstackQueryConfig } from "~/helpers/constants";
import {
  useReportListLoadCount,
  useReportListPage,
  useReportListActions,
  useReportListSearchFilter,
} from "~/store/reportListStore";
import { createErrorHandler } from "~/helpers/errorHandling.helper";
import { decryptListItem } from "~/helpers/item-decryption.helper";
import { useIsPasswordExpired } from "~/store/credentials";
import { usePasswordCheck } from "~/hooks/usePasswordCheck";
import { usePassword } from "~/store/credentials";

export const useReportCRUD = (
  folderId?: string | null,
  enableQuery: boolean = true
) => {
  const { closeDialog } = useDialogActions();
  const queryClient = useQueryClient();
  const { decryptData, _encryptData, encryptNgrams } = useEncryption();
  const { clearPendingList } = useNewPendingReportsActions();
  const pageNumber = useReportListPage();
  const limitNumber = useReportListLoadCount();
  const searchFilter = useReportListSearchFilter();
  const {
    setReportList,
    incrementReportList,
    resetPagination,
    setLoadMore,
    clearSearchFilter,
  } = useReportListActions();
  const secretKey = usePassword();
  const isPasswordExpired = useIsPasswordExpired();
  const { withPasswordCheck } = usePasswordCheck();

  const reportListQuery = useQuery({
    queryKey: ["reports", "list", folderId],
    queryFn: async ({ meta }) => {
      try {
        clearPendingList();

        let hmacFilters;
        if (searchFilter) {
          console.log("reportListQuery searchFilter", searchFilter);
          const ngrams = await encryptNgrams({
            searched_value: searchFilter,
          });
          hmacFilters = ngrams.searched_value;
          //clearSearchFilter()
        }

        const reportsListData = await fetchReportList({
          page: pageNumber,
          limit: limitNumber,
          hmacFilters,
          folderId: folderId,
        });

        const decryptedItemsList = await Promise.all(
          reportsListData.map(async (item, index) => {
            try {
              return await decryptListItem(item, decryptData);
            } catch (error) {
              console.error(`Error decrypting item #${index + 1}:`, error);
              throw error;
            }
          })
        );

        const finalList = decryptedItemsList as ReportData[];

        // const mockedData = await fetch("/mock_report_list.json");
        // const fetchedList = await mockedData.json()
        // const finalList = fetchedList as ReportData[]

        //@ts-ignore
        pageNumber === 1
          ? setReportList(finalList)
          : incrementReportList(finalList);
        const itemCount = finalList.length;
        const hasMore = Boolean(itemCount === limitNumber);
        setLoadMore(hasMore);
        console.log("decrypted items list", finalList);
        // TODO - remover caso não esteja agregando NADA - teste para evitar mensagem de refetch sempre que chamar a lista
        const isScheduledRefetch = meta?.fetchReason === "refetch";
        if (isScheduledRefetch) {
          sessionStorage.removeItem("reports_list_toast_shown");
        }

        return {
          data: finalList as (ReportData | FolderData)[],
          isScheduledRefetch: isScheduledRefetch,
        };
      } catch (err: any) {
        console.error("Error fetching report list:", err);
        toast.error("Erro", {
          description: "Ocorreu um erro ao tentar atualizar a lista.",
        });
        return {
          data: [],
          isScheduledRefetch: false,
        };
      }
    },
    ...tanstackQueryConfig,
    staleTime: Infinity,
    enabled: !isPasswordExpired && enableQuery,
    meta: {
      fetchReason: "initial",
    },
  });

  const reportDetailsQuery = (id: string) =>
    useQuery<ReportData, Error>({
      queryKey: ["reports", "details", id],
      queryFn: async () => {
        // const response = await fetchReportById(id);
        // const decrypted = await decryptReportPayload(
        //   response as ReportData,
        //   decryptData
        // );
        // console.log("[useReportCRUD] reportDetailsQuery decrypted", decrypted);
        // return decrypted as ReportData;

        const repoonse = await fetch("/mock_retorno_combinado.json");
        const data = await repoonse.json();
        console.log("[useReportCRUD] reportDetailsQuery data", data);
        return data as ReportData;
      },
      staleTime: Infinity,
      enabled: !!id && !isPasswordExpired,
    });

  const newReportMutation = useMutation({
    mutationFn: async (report: NewReportRequest) => {
      try {
        const ngrams = await encryptNgrams({
          report_search_args: report.report_search_args,
        });

        const { report_search_args, ...newReport } = report;

        const payload = {
          ...newReport,
          [REPORT_CONSTANTS.new_report.hmac]: {
            [REPORT_CONSTANTS.new_report.ngrams]: ngrams,
          },
        };

        return createNewReport(payload);
      } catch (error) {
        console.error("Error creating new report in SNAP API:", error);
        throw new Error("Failed to create new report in SNAP API");
      }
    },
    onSuccess: async (data, variables) => {
      closeDialog();
      toast.success("Gerando novo relatório...");
      window.dispatchEvent(new Event("snap:report-created"));
      withPasswordCheck(() => {
        invalidateCurrentFolderNoFilters(variables.parent_folder_id || null);
      });
    },
    onError: createErrorHandler(
      "Ocorreu um erro ao tentar criar um novo relatório",
      "Erro ao criar relatório"
    ),
  });

  const addNewReportMutation = useMutation({
    mutationFn: async (report: any) => {
      try {
        const { user_reports_id } = report;
        if (user_reports_id) {
          const ngrams = await encryptNgrams({
            report_name: report.report_name,
            report_search_args: report.report_search_args,
            subject_name: report.subject_name,
            subject_mother_name: report.subject_mother_name,
          });

          /* não enviar mais created_at e modified_at */
          const { created_at, modified_at, ...newReport } = report;
          const modifiedReports = {
            ...newReport,
            [REPORT_CONSTANTS.new_report.report_status]:
              REPORT_CONSTANTS.status.completed,
          };
          const encryptedReport = await encryptReportPayload(
            modifiedReports,
            secretKey,
            _encryptData
          );
          const payload = {
            ...encryptedReport,
            [REPORT_CONSTANTS.new_report.hmac]: {
              [REPORT_CONSTANTS.new_report.ngrams]: ngrams,
            }, // formato exigido pelo backend
          };

          return addNewReport(payload, user_reports_id);
        }
      } catch (error) {
        console.error("Error creating new report:", error);
        throw new Error("Failed to create new report");
      }
    },
  });

  const mergeReportsMutation = useMutation({
    mutationFn: async (payload: MergeReportsProps) => {
      const { reportsSelectedIdList } = payload;

      if (!reportsSelectedIdList || reportsSelectedIdList.length === 0) {
        throw new Error("No reports selected for merging");
      }

      try {
        const reportsDetails = await fetchAndDecryptReports(
          reportsSelectedIdList,
          fetchReportById,
          decryptReportPayload,
          decryptData
        );

        if (!reportsDetails || reportsDetails.length === 0) {
          throw new Error("Failed to fetch report details");
        }

        const allNgrams = await combineNgramsFromReports(reportsDetails, encryptNgrams);
        const combinedReportSearchArgs = combineReportSearchArgs(reportsDetails);

        const derivedKey = base64ToUint8Array(secretKey || "");
        const encryptedSearchArgs = await _encryptData(combinedReportSearchArgs, derivedKey.buffer)

        const mergeReportsPayload = {
          [REPORT_CONSTANTS.new_report.report_type]: REPORT_CONSTANTS.types.combinado,
          [REPORT_CONSTANTS.new_report.report_input_value]: combinedReportSearchArgs,
          [REPORT_CONSTANTS.new_report.report_input_encrypted]: encryptedSearchArgs.data,
          [REPORT_CONSTANTS.new_report.hmac]: {
            [REPORT_CONSTANTS.new_report.ngrams]: {
              report_search_args: allNgrams
            },
          },
          [REPORT_CONSTANTS.merge_reports.reports_to_merge]: reportsDetails,
        };

        console.log("[ useReportCRUD ] mergeReportsPayload", mergeReportsPayload);

        return mergeReports(mergeReportsPayload as MergeReportsPayload);
      } catch (error) {
        console.error("Error merging reports:", error);
        throw new Error("Failed to merge reports");
      }
    },
    onSuccess: async (data, variables) => {
      closeDialog();
      toast.success("Gerando novo relatório...");
      window.dispatchEvent(new Event("snap:report-created"));
      withPasswordCheck(() => {
        invalidateCurrentFolderNoFilters(
          variables.parent_folder_id || null
        );
      });
    },
    onError: createErrorHandler(
      "Ocorreu um erro ao tentar combinar os relatórios",
      "Erro ao combinar relatórios"
    ),
  })

  const deleteReportMutation = useMutation({
    mutationFn: async (reportId: string) => {
      try {
        await deleteReport(reportId);
      } catch (error) {
        console.error("Error deleting report:", error);
        throw error;
      }
    },
    onSuccess: async (data: any) => {
      closeDialog();
      toast.success(`Relatório deletado com sucesso!`);
      withPasswordCheck(() => {
        invalidateCurrentFolder(folderId || null);
      });
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar deletar o relatório",
        "Erro ao deletar relatório"
      )(error);
    },
  });

  const invalidateToInitialPage = async () => {
    await resetPagination();
    await queryClient.invalidateQueries({
      queryKey: ["reports", "list"],
      exact: false,
    });
  };

  const invalidateReportDetails = async (id: string) => {
    await queryClient.invalidateQueries({
      queryKey: ["reports", "details", id],
      exact: true,
    });
  };

  const setReportDetailsToStale = async (id: string) => {
    await queryClient.invalidateQueries({
      queryKey: ["reports", "details", id],
      exact: true,
      refetchType: "none", // Marca como stale mas não faz refetch imediatamente
    });
  };

  // resetQueries remove os dados do cache forçando refetch na próxima renderização
  const resetReportListQuery = async () => {
    await queryClient.resetQueries({
      queryKey: ["reports", "list"],
      exact: true,
    });
  };

  const invalidateCurrentFolder = (targetFolderId: string | null) => {
    queryClient.invalidateQueries({
      queryKey: ["reports", "list", targetFolderId],
      exact: true,
    });
  };

  const invalidateAllReports = () => {
    queryClient.invalidateQueries({
      queryKey: ["reports", "list"],
      exact: false,
    });
  };

  const invalidateCurrentFolderNoFilters = async (
    targetFolderId: string | null
  ) => {
    await resetPagination();
    await clearSearchFilter(true);
    await queryClient.invalidateQueries({
      queryKey: ["reports", "list", targetFolderId],
      exact: true,
    });
  };

  const generatePDFMutation = useMutation({
    mutationFn: async (props: ReportPDFProps) => {
      try {
        const url = await generatePDF(props);
        return url;
      } catch (error) {
        console.error("Error generating PDF:", error);
        throw error;
      }
    },
    onError: (error: Error) => {
      console.error("Error generating PDF:", error);
    },
  });

  const renameReportMutation = useMutation({
    mutationFn: async ({
      report_id,
      report_name,
    }: {
      report_id: string;
      report_name: string;
    }) => {
      try {
        const hmacResult = await encryptNgrams({
          report_name: report_name,
        });
        const derivedKey = base64ToUint8Array(secretKey || "");
        const encryptedReportName = await _encryptData(
          report_name,
          derivedKey.buffer
        );

        if (!encryptedReportName.success || !encryptedReportName.data) {
          throw new Error("Erro ao criptografar nome do relatório");
        }

        await renameReport({
          [REPORT_CONSTANTS.new_report.report_id]: report_id,
          [REPORT_CONSTANTS.new_report.report_name]:
            encryptedReportName.data as EncryptedPayload,
          [REPORT_CONSTANTS.new_report.hmac]: {
            [REPORT_CONSTANTS.new_report.ngrams]: hmacResult,
          },
        });
      } catch (error) {
        console.error("Error renaming report:", error);
        throw error;
      }
    },
    onSuccess: async (data: any) => {
      closeDialog();
      toast.success(`Relatório renomeado com sucesso!`);
      withPasswordCheck(() => {
        invalidateCurrentFolderNoFilters(folderId || null);
      });
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar renomear o relatório",
        "Erro ao renomear relatório"
      )(error);
    },
  });

  return {
    reportListQuery,
    reportDetailsQuery,
    invalidateToInitialPage,
    invalidateReportDetails,
    setReportDetailsToStale,
    resetReportListQuery,
    invalidateCurrentFolder,
    invalidateAllReports,
    invalidateCurrentFolderNoFilters,
    newReportMutation,
    addNewReportMutation,
    deleteReportMutation,
    generatePDFMutation,
    renameReportMutation,
    mergeReportsMutation,
  };
};
