import { Text } from "@snap/design-system";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Label } from "~/components/ui/label";

interface ReportModeSelectorProps {
  reportMode: "individual" | "relacoes";
  onReportModeChange: (mode: "individual" | "relacoes") => void;
}

export function ReportModeSelector({ reportMode, onReportModeChange }: ReportModeSelectorProps) {
  return (
    <div>
      <RadioGroup
        value={reportMode}
        onValueChange={(value) => onReportModeChange(value as "individual" | "relacoes")}
        className="flex flex-row gap-6"
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="individual" id="individual" className="border-foreground cursor-pointer" />
          <Label htmlFor="individual" className="cursor-pointer">Individual</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="relacoes" id="relacoes" className="border-foreground cursor-pointer" />
          <Label htmlFor="relacoes" className="cursor-pointer">Relações</Label>
        </div>
      </RadioGroup>
    </div>
  );
}
